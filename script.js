// إعدادات التطبيق
class VideoIdeaGenerator {
    constructor() {
        this.apiKeys = [];
        this.currentKeyIndex = 0;
        this.generatedIdeas = this.loadHistory();
        this.currentIdeaType = 'cutting';
        this.init();
    }

    init() {
        this.loadApiKeys();
        this.bindEvents();
        this.updateHistoryDisplay();
    }

    // تحميل مفاتيح API من localStorage أو من المدخلات
    loadApiKeys() {
        const savedKeys = localStorage.getItem('gemini_api_keys');
        if (savedKeys) {
            this.apiKeys = JSON.parse(savedKeys);
        }
        
        // تحديث واجهة المستخدم
        const keySelector = document.getElementById('keySelector');
        const apiKeyInput = document.getElementById('apiKey');
        
        if (this.apiKeys.length > 0) {
            apiKeyInput.value = this.apiKeys[this.currentKeyIndex] || '';
        }
    }

    // حفظ مفاتيح API
    saveApiKeys() {
        localStorage.setItem('gemini_api_keys', JSON.stringify(this.apiKeys));
    }

    // ربط الأحداث
    bindEvents() {
        // زر توليد الأفكار
        document.getElementById('generateBtn').addEventListener('click', () => {
            this.generateIdea();
        });

        // اختيار نوع الفكرة
        document.querySelectorAll('.idea-type').forEach(type => {
            type.addEventListener('click', (e) => {
                if (!e.currentTarget.classList.contains('disabled')) {
                    document.querySelectorAll('.idea-type').forEach(t => t.classList.remove('active'));
                    e.currentTarget.classList.add('active');
                    this.currentIdeaType = e.currentTarget.dataset.type;
                }
            });
        });

        // تغيير مفتاح API
        document.getElementById('keySelector').addEventListener('change', (e) => {
            this.currentKeyIndex = parseInt(e.target.value) - 1;
            const apiKeyInput = document.getElementById('apiKey');
            if (this.apiKeys[this.currentKeyIndex]) {
                apiKeyInput.value = this.apiKeys[this.currentKeyIndex];
            }
        });

        // حفظ مفتاح API عند التغيير
        document.getElementById('apiKey').addEventListener('input', (e) => {
            const keyIndex = this.currentKeyIndex;
            if (!this.apiKeys[keyIndex]) {
                this.apiKeys[keyIndex] = '';
            }
            this.apiKeys[keyIndex] = e.target.value;
            this.saveApiKeys();
        });

        // مسح التاريخ
        document.getElementById('clearHistoryBtn').addEventListener('click', () => {
            this.clearHistory();
        });
    }

    // توليد فكرة جديدة
    async generateIdea() {
        const apiKey = document.getElementById('apiKey').value.trim();
        
        if (!apiKey) {
            this.showToast('يرجى إدخال مفتاح Gemini API أولاً', 'error');
            return;
        }

        const generateBtn = document.getElementById('generateBtn');
        const loading = document.getElementById('loading');
        
        generateBtn.disabled = true;
        loading.style.display = 'block';

        try {
            const idea = await this.callGeminiAPI(apiKey);
            this.displayResult(idea);
            this.addToHistory(idea);
            this.showToast('تم توليد الفكرة بنجاح!');
        } catch (error) {
            console.error('خطأ في توليد الفكرة:', error);
            this.showToast('حدث خطأ في توليد الفكرة. يرجى المحاولة مرة أخرى.', 'error');
        } finally {
            generateBtn.disabled = false;
            loading.style.display = 'none';
        }
    }

    // استدعاء Gemini API
    async callGeminiAPI(apiKey) {
        const prompt = this.buildPrompt();

        const response = await fetch(`${CONFIG.GEMINI.BASE_URL}?key=${apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: CONFIG.GEMINI.GENERATION_CONFIG
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
            throw new Error('استجابة غير صالحة من API');
        }

        return this.parseGeminiResponse(data.candidates[0].content.parts[0].text);
    }

    // بناء البرومت للذكاء الاصطناعي
    buildPrompt() {
        const previousIdeas = this.generatedIdeas.map(idea => idea.concept).join('\n- ');
        const template = CONFIG.PROMPT_TEMPLATES[this.currentIdeaType];

        return template.replace('{PREVIOUS_IDEAS}',
            previousIdeas ? '- ' + previousIdeas : 'لا توجد أفكار سابقة');
    }

    // تحليل استجابة Gemini
    parseGeminiResponse(text) {
        const lines = text.split('\n');
        const result = {
            concept: '',
            prompt: '',
            title: '',
            hashtags: '',
            timestamp: new Date().toISOString()
        };

        let currentSection = '';
        let content = '';

        for (const line of lines) {
            const trimmedLine = line.trim();
            
            if (trimmedLine.startsWith('CONCEPT:')) {
                if (currentSection && content) {
                    result[currentSection] = content.trim();
                }
                currentSection = 'concept';
                content = trimmedLine.replace('CONCEPT:', '').trim();
            } else if (trimmedLine.startsWith('PROMPT:')) {
                if (currentSection && content) {
                    result[currentSection] = content.trim();
                }
                currentSection = 'prompt';
                content = trimmedLine.replace('PROMPT:', '').trim();
            } else if (trimmedLine.startsWith('TITLE:')) {
                if (currentSection && content) {
                    result[currentSection] = content.trim();
                }
                currentSection = 'title';
                content = trimmedLine.replace('TITLE:', '').trim();
            } else if (trimmedLine.startsWith('HASHTAGS:')) {
                if (currentSection && content) {
                    result[currentSection] = content.trim();
                }
                currentSection = 'hashtags';
                content = trimmedLine.replace('HASHTAGS:', '').trim();
            } else if (trimmedLine && currentSection) {
                content += '\n' + trimmedLine;
            }
        }

        // حفظ القسم الأخير
        if (currentSection && content) {
            result[currentSection] = content.trim();
        }

        return result;
    }

    // عرض النتيجة
    displayResult(idea) {
        const resultsSection = document.getElementById('resultsSection');
        const ideaContent = document.getElementById('ideaContent');
        const promptContent = document.getElementById('promptContent');
        const titleHashtagsContent = document.getElementById('titleHashtagsContent');

        ideaContent.textContent = idea.concept;
        promptContent.textContent = idea.prompt;
        titleHashtagsContent.textContent = `${idea.title}\n\n${idea.hashtags}`;

        resultsSection.style.display = 'block';
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    // إضافة إلى التاريخ
    addToHistory(idea) {
        this.generatedIdeas.unshift(idea);
        // الاحتفاظ بآخر عدد محدد من الأفكار
        if (this.generatedIdeas.length > CONFIG.APP.MAX_HISTORY_ITEMS) {
            this.generatedIdeas = this.generatedIdeas.slice(0, CONFIG.APP.MAX_HISTORY_ITEMS);
        }
        this.saveHistory();
        this.updateHistoryDisplay();
    }

    // تحديث عرض التاريخ
    updateHistoryDisplay() {
        const historySection = document.getElementById('historySection');
        const clearBtn = document.getElementById('clearHistoryBtn');

        if (this.generatedIdeas.length === 0) {
            historySection.innerHTML = '<p class="no-history">لا توجد أفكار مولدة بعد</p>';
            clearBtn.style.display = 'none';
        } else {
            let historyHTML = '';
            this.generatedIdeas.forEach((idea, index) => {
                const date = new Date(idea.timestamp).toLocaleDateString('ar-SA');
                historyHTML += `
                    <div class="history-item">
                        <h4>فكرة ${index + 1} - ${date}</h4>
                        <p><strong>الفكرة:</strong> ${idea.concept}</p>
                        <p><strong>العنوان:</strong> ${idea.title}</p>
                    </div>
                `;
            });
            historySection.innerHTML = historyHTML;
            clearBtn.style.display = 'block';
        }
    }

    // تحميل التاريخ
    loadHistory() {
        const saved = localStorage.getItem('video_ideas_history');
        return saved ? JSON.parse(saved) : [];
    }

    // حفظ التاريخ
    saveHistory() {
        localStorage.setItem('video_ideas_history', JSON.stringify(this.generatedIdeas));
    }

    // مسح التاريخ
    clearHistory() {
        if (confirm('هل أنت متأكد من مسح جميع الأفكار المولدة؟')) {
            this.generatedIdeas = [];
            this.saveHistory();
            this.updateHistoryDisplay();
            this.showToast('تم مسح التاريخ بنجاح');
        }
    }

    // عرض إشعار
    showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.className = `toast ${type}`;
        toast.classList.add('show');

        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }
}

// وظائف مساعدة
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;

    navigator.clipboard.writeText(text).then(() => {
        // إنشاء إشعار نجاح
        const toast = document.getElementById('toast');
        toast.textContent = 'تم نسخ النص بنجاح!';
        toast.className = 'toast success';
        toast.classList.add('show');

        setTimeout(() => {
            toast.classList.remove('show');
        }, 2000);
    }).catch(err => {
        console.error('فشل في نسخ النص:', err);
        // إنشاء إشعار خطأ
        const toast = document.getElementById('toast');
        toast.textContent = 'فشل في نسخ النص';
        toast.className = 'toast error';
        toast.classList.add('show');

        setTimeout(() => {
            toast.classList.remove('show');
        }, 2000);
    });
}

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new VideoIdeaGenerator();
});
