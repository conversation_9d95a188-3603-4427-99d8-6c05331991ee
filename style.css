* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5rem;
    color: #4a5568;
    margin-bottom: 10px;
    font-weight: 700;
}

.header p {
    font-size: 1.1rem;
    color: #718096;
    font-weight: 400;
}

.main-content {
    display: grid;
    gap: 30px;
}

.section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.section h2 {
    font-size: 1.5rem;
    color: #2d3748;
    margin-bottom: 20px;
    font-weight: 600;
}

.idea-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.idea-type {
    padding: 25px;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f7fafc;
}

.idea-type:hover:not(.disabled) {
    border-color: #667eea;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
}

.idea-type.active {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.idea-type.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.idea-type i {
    font-size: 2rem;
    margin-bottom: 15px;
    display: block;
}

.idea-type h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.api-settings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group label {
    font-weight: 600;
    color: #4a5568;
}

.input-group input,
.input-group select {
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #667eea;
}

.generate-section {
    text-align: center;
}

.generate-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.1rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
}

.generate-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.generate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.loading {
    margin-top: 20px;
    font-size: 1.1rem;
    color: #667eea;
}

.result-card {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
}

.result-card h3 {
    font-size: 1.3rem;
    color: #2d3748;
    margin-bottom: 15px;
    font-weight: 600;
}

.content {
    background: white;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    line-height: 1.6;
    white-space: pre-wrap;
}

.content div {
    margin-bottom: 10px;
}

.content strong {
    color: #4a5568;
    font-weight: 600;
}

.content-with-copy {
    position: relative;
}

.copy-btn {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.copy-btn:hover {
    background: #5a67d8;
    transform: scale(1.05);
}

.clear-btn {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 10px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
}

.clear-btn:hover {
    background: #c53030;
}

.no-history {
    text-align: center;
    color: #718096;
    font-style: italic;
    padding: 20px;
}

.history-item {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
}

.history-item h4 {
    color: #2d3748;
    margin-bottom: 10px;
    font-weight: 600;
}

.history-item p {
    color: #4a5568;
    font-size: 0.9rem;
    line-height: 1.5;
}

.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #48bb78;
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
}

.toast.show {
    transform: translateX(0);
}

.toast.error {
    background: #e53e3e;
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .api-settings {
        grid-template-columns: 1fr;
    }
    
    .idea-types {
        grid-template-columns: 1fr;
    }
    
    .copy-btn {
        position: static;
        margin-top: 10px;
        width: 100%;
    }
}
