// إعدادات التطبيق
const CONFIG = {
    // إعدادات Gemini API
    GEMINI: {
        BASE_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent',
        MAX_RETRIES: 3,
        TIMEOUT: 30000, // 30 ثانية
        GENERATION_CONFIG: {
            temperature: 0.9,
            topK: 1,
            topP: 1,
            maxOutputTokens: 2048,
        }
    },

    // إعدادات التطبيق
    APP: {
        MAX_HISTORY_ITEMS: 10,
        AUTO_SAVE: true,
        LANGUAGE: 'ar',
        THEME: 'default'
    },

    // أنواع الأفكار المدعومة
    IDEA_TYPES: {
        cutting: {
            name: 'مقاطع التقطيع',
            description: 'أفكار تقطيع أشياء غريبة ومجذبة',
            icon: 'fas fa-cut',
            enabled: true
        },
        cooking: {
            name: 'الطبخ السريع',
            description: 'أفكار طبخ سريعة ومثيرة',
            icon: 'fas fa-fire',
            enabled: false // سيتم تفعيلها لاحقاً
        },
        unboxing: {
            name: 'فتح الصناديق',
            description: 'أفكار فتح صناديق مثيرة',
            icon: 'fas fa-box-open',
            enabled: false // سيتم تفعيلها لاحقاً
        },
        transformation: {
            name: 'التحويلات',
            description: 'أفكار تحويل الأشياء',
            icon: 'fas fa-magic',
            enabled: false // سيتم تفعيلها لاحقاً
        }
    },

    // قوالب البرومت لكل نوع
    PROMPT_TEMPLATES: {
        cutting: `أنت مولد أفكار إبداعية لمقاطع الشورتس المجذبة للانتباه. مهمتك هي إنشاء فكرة جديدة ومبتكرة لمقطع تقطيع أشياء غريبة وعجيبة.

المتطلبات:
1. الفكرة يجب أن تكون حول شخص يحمل سكين ويقطع أشياء فوق طاولة مطبخ
2. لا يظهر وجه الشخص، فقط السكين والأشياء التي يقطعها
3. الأشياء يجب أن تكون متنوعة ومثيرة للاهتمام مثل:
   - فواكه غريبة وعجيبة
   - نماذج من كواكب
   - شخصيات أنمي (مثل فواكه ون بيس)
   - أشياء خيالية من عالم الأنمي والأفلام
   - أشياء واقعية غريبة: كوكاكولا، شيبس، سيارة صغيرة، سلاح لعبة، همبرغر، ماس، ذهب، جهاز تحكم، كأس ماء، جمجمة، إلخ

الأفكار المولدة سابقاً (تجنب تكرارها):
{PREVIOUS_IDEAS}

يرجى تقديم الإجابة بالتنسيق التالي بالضبط:

CONCEPT_AR: [وصف مختصر للفكرة الأساسية باللغة العربية]

CONCEPT_EN: [Brief description of the main concept in English]

PROMPT: [برومت مفصل لـ Veo 3 لإنشاء الفيديو، يجب أن يكون باللغة الإنجليزية ومفصل جداً]

TITLE_AR: [عنوان جذاب للفيديو باللغة العربية يحتوي على إيموجي واحد على الأقل]

TITLE_EN: [Attractive video title in English with at least one emoji]

HASHTAGS: [هاشتاقات مناسبة لجلب المشاهدات، مفصولة بمسافات، باللغتين العربية والإنجليزية]

تأكد من أن الفكرة جديدة ومختلفة عن الأفكار السابقة.`
    },

    // قائمة الأشياء المقترحة للتقطيع
    CUTTING_ITEMS: {
        fruits: [
            'تفاح ذهبي عملاق', 'برتقال أزرق', 'موز أرجواني', 'فراولة عملاقة',
            'أناناس صغير', 'عنب كريستالي', 'خوخ متوهج', 'كيوي عملاق'
        ],
        anime: [
            'فاكهة الشيطان من ون بيس', 'كرة التنين', 'بوكيمون صغير',
            'سيف من أنمي', 'قناع أنمي', 'شخصية أنمي صغيرة'
        ],
        planets: [
            'نموذج الأرض', 'نموذج المريخ', 'نموذج القمر', 'نموذج الشمس',
            'كوكب خيالي', 'نيزك صغير', 'قمر صناعي'
        ],
        everyday: [
            'علبة كوكاكولا', 'كيس شيبس', 'سيارة لعبة', 'همبرغر بلاستيكي',
            'جهاز تحكم', 'هاتف قديم', 'ساعة', 'نظارات', 'قلم عملاق',
            'كأس ماء ملون', 'صابون', 'شمعة', 'كتاب صغير'
        ],
        precious: [
            'قطعة ذهب مقلدة', 'ماسة مقلدة', 'لؤلؤة كبيرة', 'عملة ذهبية',
            'خاتم لامع', 'سلسلة ذهبية', 'حجر كريم مقلد'
        ],
        weird: [
            'جمجمة بلاستيكية', 'عين مقلدة', 'يد مطاطية', 'ثعبان مطاطي',
            'عنكبوت كبير', 'فأر لعبة', 'وحش صغير', 'كائن فضائي'
        ]
    },

    // الهاشتاقات الشائعة
    POPULAR_HASHTAGS: [
        '#shorts', '#viral', '#satisfying', '#oddlysatisfying', '#cutting',
        '#asmr', '#trending', '#fyp', '#foryou', '#amazing', '#wow',
        '#knife', '#kitchen', '#food', '#art', '#creative', '#unique',
        '#strange', '#weird', '#cool', '#awesome', '#epic', '#mind_blown'
    ],

    // إعدادات التصميم
    UI: {
        ANIMATION_DURATION: 300,
        TOAST_DURATION: 3000,
        SCROLL_BEHAVIOR: 'smooth',
        THEME_COLORS: {
            primary: '#667eea',
            secondary: '#764ba2',
            success: '#48bb78',
            error: '#e53e3e',
            warning: '#ed8936',
            info: '#4299e1'
        }
    }
};

// تصدير الإعدادات للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
