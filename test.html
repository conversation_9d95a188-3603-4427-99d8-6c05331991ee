<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مولد أفكار الشورتس</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            padding: 20px;
            direction: rtl;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            color: #333;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        .test-result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .mock-response {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار مولد أفكار الشورتس</h1>
        
        <div class="test-section">
            <h2 class="test-title">1. اختبار تحميل الإعدادات</h2>
            <button onclick="testConfigLoading()">اختبار تحميل CONFIG</button>
            <div id="configTest" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2 class="test-title">2. اختبار بناء البرومت</h2>
            <button onclick="testPromptBuilding()">اختبار بناء البرومت</button>
            <div id="promptTest" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2 class="test-title">3. اختبار تحليل الاستجابة</h2>
            <button onclick="testResponseParsing()">اختبار تحليل الاستجابة</button>
            <div id="parseTest" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2 class="test-title">4. اختبار حفظ واسترجاع التاريخ</h2>
            <button onclick="testHistoryManagement()">اختبار إدارة التاريخ</button>
            <div id="historyTest" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2 class="test-title">5. محاكاة استجابة Gemini</h2>
            <button onclick="testMockGeminiResponse()">اختبار استجابة وهمية</button>
            <div id="mockTest" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2 class="test-title">6. اختبار شامل</h2>
            <button onclick="runAllTests()">تشغيل جميع الاختبارات</button>
            <div id="allTests" class="test-result"></div>
        </div>
    </div>

    <script src="config.js"></script>
    <script>
        // محاكاة كلاس VideoIdeaGenerator للاختبار
        class TestVideoIdeaGenerator {
            constructor() {
                this.generatedIdeas = [];
                this.currentIdeaType = 'cutting';
            }

            buildPrompt() {
                const previousIdeas = this.generatedIdeas.map(idea => idea.concept).join('\n- ');
                const template = CONFIG.PROMPT_TEMPLATES[this.currentIdeaType];
                return template.replace('{PREVIOUS_IDEAS}', 
                    previousIdeas ? '- ' + previousIdeas : 'لا توجد أفكار سابقة');
            }

            parseGeminiResponse(text) {
                const lines = text.split('\n');
                const result = {
                    concept: '',
                    prompt: '',
                    title: '',
                    hashtags: '',
                    timestamp: new Date().toISOString()
                };

                let currentSection = '';
                let content = '';

                for (const line of lines) {
                    const trimmedLine = line.trim();
                    
                    if (trimmedLine.startsWith('CONCEPT:')) {
                        if (currentSection && content) {
                            result[currentSection] = content.trim();
                        }
                        currentSection = 'concept';
                        content = trimmedLine.replace('CONCEPT:', '').trim();
                    } else if (trimmedLine.startsWith('PROMPT:')) {
                        if (currentSection && content) {
                            result[currentSection] = content.trim();
                        }
                        currentSection = 'prompt';
                        content = trimmedLine.replace('PROMPT:', '').trim();
                    } else if (trimmedLine.startsWith('TITLE:')) {
                        if (currentSection && content) {
                            result[currentSection] = content.trim();
                        }
                        currentSection = 'title';
                        content = trimmedLine.replace('TITLE:', '').trim();
                    } else if (trimmedLine.startsWith('HASHTAGS:')) {
                        if (currentSection && content) {
                            result[currentSection] = content.trim();
                        }
                        currentSection = 'hashtags';
                        content = trimmedLine.replace('HASHTAGS:', '').trim();
                    } else if (trimmedLine && currentSection) {
                        content += '\n' + trimmedLine;
                    }
                }

                if (currentSection && content) {
                    result[currentSection] = content.trim();
                }

                return result;
            }
        }

        const testGenerator = new TestVideoIdeaGenerator();

        function testConfigLoading() {
            const resultDiv = document.getElementById('configTest');
            try {
                if (typeof CONFIG !== 'undefined') {
                    resultDiv.innerHTML = `<span class="success">✅ تم تحميل الإعدادات بنجاح</span>
                        <br>عدد أنواع الأفكار: ${Object.keys(CONFIG.IDEA_TYPES).length}
                        <br>عدد الهاشتاقات: ${CONFIG.POPULAR_HASHTAGS.length}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ فشل في تحميل الإعدادات</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ خطأ: ${error.message}</span>`;
            }
        }

        function testPromptBuilding() {
            const resultDiv = document.getElementById('promptTest');
            try {
                const prompt = testGenerator.buildPrompt();
                if (prompt && prompt.includes('CONCEPT:') && prompt.includes('PROMPT:')) {
                    resultDiv.innerHTML = `<span class="success">✅ تم بناء البرومت بنجاح</span>
                        <div class="mock-response">${prompt.substring(0, 200)}...</div>`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ البرومت غير صحيح</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ خطأ: ${error.message}</span>`;
            }
        }

        function testResponseParsing() {
            const resultDiv = document.getElementById('parseTest');
            const mockResponse = `CONCEPT: تقطيع فاكهة التنين الذهبية

PROMPT: A close-up shot of hands holding a sharp kitchen knife, cutting a golden dragon fruit on a wooden cutting board. The camera focuses on the knife blade as it slices through the exotic fruit, revealing the unique interior. No face is visible, only hands and the cutting action. Professional lighting, 4K quality, satisfying cutting sounds.

TITLE: 🐉 تقطيع فاكهة التنين الذهبية - مذهل!

HASHTAGS: #shorts #satisfying #cutting #dragonfruit #asmr #viral #fyp #food #exotic #amazing`;

            try {
                const parsed = testGenerator.parseGeminiResponse(mockResponse);
                if (parsed.concept && parsed.prompt && parsed.title && parsed.hashtags) {
                    resultDiv.innerHTML = `<span class="success">✅ تم تحليل الاستجابة بنجاح</span>
                        <br><strong>الفكرة:</strong> ${parsed.concept}
                        <br><strong>العنوان:</strong> ${parsed.title}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ فشل في تحليل الاستجابة</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ خطأ: ${error.message}</span>`;
            }
        }

        function testHistoryManagement() {
            const resultDiv = document.getElementById('historyTest');
            try {
                // اختبار حفظ واسترجاع
                const testIdea = {
                    concept: 'فكرة اختبار',
                    prompt: 'برومت اختبار',
                    title: 'عنوان اختبار',
                    hashtags: '#test #اختبار',
                    timestamp: new Date().toISOString()
                };

                localStorage.setItem('test_video_ideas', JSON.stringify([testIdea]));
                const retrieved = JSON.parse(localStorage.getItem('test_video_ideas'));

                if (retrieved && retrieved[0].concept === 'فكرة اختبار') {
                    resultDiv.innerHTML = `<span class="success">✅ إدارة التاريخ تعمل بشكل صحيح</span>`;
                    localStorage.removeItem('test_video_ideas'); // تنظيف
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ مشكلة في إدارة التاريخ</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ خطأ: ${error.message}</span>`;
            }
        }

        function testMockGeminiResponse() {
            const resultDiv = document.getElementById('mockTest');
            const mockApiResponse = {
                candidates: [{
                    content: {
                        parts: [{
                            text: `CONCEPT: تقطيع كوكب المريخ المصغر

PROMPT: Close-up view of hands using a sharp knife to cut through a detailed miniature Mars planet model on a kitchen cutting board. The model shows realistic surface features and red coloration. Camera focuses on the cutting action, showing the cross-section of the planet model. No face visible, only hands and knife. High-quality 4K video with satisfying cutting sounds.

TITLE: 🪐 تقطيع كوكب المريخ - لن تصدق ما بداخله!

HASHTAGS: #shorts #mars #planet #cutting #satisfying #space #viral #fyp #amazing #science #asmr #cool`
                        }]
                    }
                }]
            };

            try {
                const parsed = testGenerator.parseGeminiResponse(mockApiResponse.candidates[0].content.parts[0].text);
                resultDiv.innerHTML = `<span class="success">✅ محاكاة الاستجابة نجحت</span>
                    <div class="mock-response">الفكرة: ${parsed.concept}
العنوان: ${parsed.title}
الهاشتاقات: ${parsed.hashtags}</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ خطأ في المحاكاة: ${error.message}</span>`;
            }
        }

        function runAllTests() {
            const resultDiv = document.getElementById('allTests');
            resultDiv.innerHTML = '<span class="warning">⏳ جاري تشغيل جميع الاختبارات...</span>';
            
            setTimeout(() => {
                testConfigLoading();
                testPromptBuilding();
                testResponseParsing();
                testHistoryManagement();
                testMockGeminiResponse();
                
                resultDiv.innerHTML = '<span class="success">✅ تم تشغيل جميع الاختبارات. تحقق من النتائج أعلاه.</span>';
            }, 1000);
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.onload = function() {
            testConfigLoading();
        };
    </script>
</body>
</html>
