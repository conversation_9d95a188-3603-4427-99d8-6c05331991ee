# 🎬 مولد أفكار الشورتس المجذبة

أداة ذكية لتوليد أفكار مقاطع تقطيع الأشياء العجيبة والنادرة (8 ثواني - شيء واحد فقط) باستخدام Gemini 2.5 Pro API، مخصصة لإنتاج مقاطع فيديو بواسطة Veo 3.

## ✨ المميزات

- 🧠 **توليد أفكار ذكية**: استخدام Gemini 2.5 Pro لتوليد أفكار إبداعية ومتنوعة
- 🌐 **دعم اللغتين**: عرض الأفكار والعناوين باللغتين العربية والإنجليزية
- ⏱️ **مدة محددة**: مقاطع بمدة 8 ثواني بالضبط لأقصى تأثير
- 🎯 **تركيز واحد**: كل مقطع يركز على تقطيع شيء واحد عجيب فقط
- 🔮 **أشياء عجيبة**: أشياء نادرة وغريبة وخيالية لجذب الانتباه
- 🔄 **تعدد المفاتيح**: إمكانية إضافة عدة مفاتيح API والتبديل بينها
- 📝 **برومت جاهز**: توليد برومت مفصل لـ Veo 3 لإنشاء الفيديو
- 🏷️ **عناوين وهاشتاقات**: توليد عناوين جذابة وهاشتاقات مناسبة باللغتين
- 📋 **نسخ سريع**: أزرار نسخ للبرومت والعناوين والهاشتاقات
- 📚 **تاريخ الأفكار**: حفظ الأفكار المولدة سابقاً لتجنب التكرار

## 🚀 كيفية الاستخدام

### 1. إعداد مفتاح API

1. احصل على مفتاح Gemini API من [Google AI Studio](https://makersuite.google.com/app/apikey)
2. أدخل المفتاح في حقل "مفتاح Gemini API"
3. يمكنك إضافة عدة مفاتيح والتبديل بينها

### 2. توليد الأفكار

1. اختر نوع الأفكار (حالياً: مقاطع التقطيع)
2. اضغط على زر "توليد فكرة جديدة"
3. انتظر حتى يتم توليد الفكرة

### 3. أنواع الأشياء العجيبة المدعومة

- **خيالية**: كرة التنين، فاكهة الشيطان، بلورات سحرية، أحجار كريمة عملاقة
- **طبيعية نادرة**: أحافير، أحجار بركانية، مرجان متحجر، خشب متحجر
- **تقنية غريبة**: معالجات عملاقة، أقراص صلبة شفافة، شرائح إلكترونية كبيرة
- **فنية**: تماثيل مصغرة، أقنعة غريبة، آلات موسيقية مصغرة
- **غامضة**: صناديق موسيقية قديمة، بوصلات أثرية، طلاسم غامضة
- **فضائية**: نيازك، نماذج كواكب، أحجار قمرية

### 4. استخدام النتائج

- **الفكرة المولدة**: وصف مختصر للشيء الواحد الذي سيتم تقطيعه باللغتين العربية والإنجليزية
- **البرومت لـ Veo 3**: نص مفصل لإنشاء فيديو 8 ثواني (انسخه واستخدمه مع Veo 3)
- **العناوين والهاشتاقات**: جاهزة للاستخدام في منصات التواصل باللغتين العربية والإنجليزية

## 🎯 أنواع الأفكار المدعومة

### مقاطع التقطيع
- شخص يحمل سكين ويقطع أشياء غريبة
- لا يظهر وجه الشخص، فقط السكين والأشياء
- أشياء متنوعة: فواكه عجيبة، نماذج كواكب، شخصيات أنمي، أشياء واقعية غريبة

### قريباً
- أفكار جديدة ستضاف في التحديثات القادمة

## 🛠️ التقنيات المستخدمة

- **HTML5**: هيكل الصفحة
- **CSS3**: التصميم والتنسيق
- **JavaScript**: المنطق والتفاعل
- **Gemini 2.5 Pro API**: توليد الأفكار
- **Local Storage**: حفظ البيانات محلياً

## 📁 هيكل الملفات

```
├── index.html          # الصفحة الرئيسية
├── style.css           # ملف التنسيق
├── script.js           # منطق التطبيق
├── .env               # مفاتيح API (لا تشاركه)
└── README.md          # دليل الاستخدام
```

## ⚙️ إعداد البيئة

1. **إنشاء ملف .env**:
```env
GEMINI_API_KEY_1=your_first_api_key_here
GEMINI_API_KEY_2=your_second_api_key_here
GEMINI_API_KEY_3=your_third_api_key_here
```

2. **فتح الملف في المتصفح**:
   - افتح `index.html` في أي متصفح حديث
   - أو استخدم خادم محلي مثل Live Server

## 🔒 الأمان

- مفاتيح API تحفظ محلياً في المتصفح فقط
- لا يتم إرسال المفاتيح لأي خادم خارجي
- تأكد من عدم مشاركة ملف .env

## 🎨 التخصيص

يمكنك تخصيص الأداة عبر:
- تعديل ألوان CSS في `style.css`
- إضافة أنواع أفكار جديدة في `script.js`
- تعديل البرومت في دالة `buildPrompt()`

## 🐛 استكشاف الأخطاء

### خطأ في API
- تأكد من صحة مفتاح API
- تحقق من اتصال الإنترنت
- جرب مفتاح API آخر

### لا تظهر النتائج
- افتح أدوات المطور (F12) وتحقق من وحدة التحكم
- تأكد من تفعيل JavaScript في المتصفح

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يمكنك:
- فتح issue في المشروع
- التواصل مع المطور

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

---

**ملاحظة**: تأكد من الامتثال لشروط استخدام Gemini API وVeo 3 عند استخدام هذه الأداة.
